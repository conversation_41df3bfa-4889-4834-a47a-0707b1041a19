# AI服务集成说明

## 🎉 新功能概述

现在系统已经成功集成了DeepSeek AI服务，并提供了完整的AI服务配置界面。您可以在智谱AI和DeepSeek之间自由切换。

## 🚀 功能特性

### ✅ 已实现功能

1. **多AI服务支持**
   - 智谱AI (ZhipuAI) - 原有服务，内置密钥
   - DeepSeek - 新增服务，需要用户提供API密钥

2. **统一的AI调用接口**
   - 自动根据配置选择AI服务
   - 统一的错误处理和日志记录
   - 智能降级机制

3. **完整的配置界面**
   - AI设置对话框
   - 服务选择下拉框
   - 密钥配置和管理
   - 连接测试功能

4. **数据库集成**
   - 新增 `ai_service` 字段存储AI服务选择
   - 新增 `deepseek_key` 字段存储DeepSeek API密钥
   - 自动数据库升级脚本

## 📋 使用说明

### 1. 启动程序
```bash
python app.py
```

### 2. 配置AI服务
1. 登录系统后，在主界面找到 **"AI设置"** 按钮
2. 点击按钮打开AI设置对话框
3. 选择要使用的AI服务：
   - **智谱AI**: 无需额外配置，使用内置密钥
   - **DeepSeek**: 需要输入您的API密钥

### 3. DeepSeek配置步骤
1. 访问 [DeepSeek平台](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在控制台创建API密钥
4. 将密钥复制到程序的配置界面
5. 点击"测试连接"验证配置
6. 保存设置

### 4. 验证配置
- 程序会自动根据您的选择使用相应的AI服务
- 在聊天日志中可以看到使用的AI服务类型
- 如果DeepSeek配置有问题，系统会自动降级到智谱AI

## 🔧 技术实现

### 数据库结构
```sql
-- 新增字段
ALTER TABLE system_info ADD COLUMN ai_service TEXT DEFAULT 'zhipu';
ALTER TABLE system_info ADD COLUMN deepseek_key TEXT DEFAULT '';
```

### 核心文件
- `src/Message.py` - AI服务调用逻辑
- `src/ui/ai_settings.py` - AI设置对话框
- `app.py` - 主程序集成
- `add_db_fields.py` - 数据库升级脚本

### API接口
```python
# DeepSeek API调用示例
def deepseek_message(self, data, prompt=None, api_key=None):
    url = "https://api.deepseek.com/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    payload = {
        "model": "deepseek-chat",
        "messages": [...],
        "max_tokens": 1024,
        "temperature": 0.8
    }
```

## 🧪 测试工具

### 1. 集成测试
```bash
python test_ai_integration.py
```

### 2. DeepSeek API测试
```bash
python test_deepseek.py
```

### 3. 数据库字段添加
```bash
python add_db_fields.py
```

## 📊 配置选项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| ai_service | AI服务选择 | 'zhipu' |
| deepseek_key | DeepSeek API密钥 | '' |

## 🔍 调试信息

程序运行时会输出详细的调试信息：
- `[DEBUG] 使用AI服务: zhipu/deepseek`
- `[DEBUG] 调用DeepSeek API，消息: xxx`
- `[DEBUG] DeepSeek返回: xxx`
- `[ERROR] DeepSeek API请求失败: xxx`

## ⚠️ 注意事项

1. **API密钥安全**
   - DeepSeek API密钥会存储在本地数据库中
   - 请妥善保管您的API密钥
   - 不要在公共场所展示密钥

2. **网络连接**
   - DeepSeek需要稳定的网络连接
   - 如果网络不稳定，系统会自动降级到智谱AI

3. **费用控制**
   - DeepSeek是付费服务，请注意API调用费用
   - 建议设置合理的使用限制

4. **兼容性**
   - 新功能向后兼容
   - 如果没有配置DeepSeek，系统默认使用智谱AI

## 🆘 故障排除

### 常见问题

1. **AI设置按钮不显示**
   - 检查程序是否正常启动
   - 重新启动程序

2. **DeepSeek连接失败**
   - 检查API密钥是否正确
   - 检查网络连接
   - 验证DeepSeek账户余额

3. **数据库错误**
   - 运行 `python add_db_fields.py` 添加必要字段
   - 检查数据库文件权限

### 日志分析
查看控制台输出的调试信息，根据错误类型进行相应处理：
- `[ERROR] DeepSeek API请求失败` - 检查网络和密钥
- `[ERROR] DeepSeek API响应格式错误` - 可能是API变更
- `[ERROR] DeepSeek处理异常` - 系统内部错误

## 🎯 下一步计划

1. 添加更多AI服务支持（如OpenAI、Claude等）
2. 实现AI服务负载均衡
3. 添加AI回复质量评估
4. 支持自定义AI模型参数

## 📞 技术支持

如有问题，请：
1. 查看控制台调试信息
2. 运行测试脚本验证配置
3. 检查网络连接和API密钥
4. 联系技术支持
