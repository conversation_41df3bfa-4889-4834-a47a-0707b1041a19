# AI设置集成到系统设置页面 - 使用指南

## 🎉 功能概述

现在AI设置已经完全集成到系统设置页面中，不再是悬浮的对话框。您可以在系统设置页面直接配置所有AI相关选项，包括智能回复开关和AI服务选择。

## ✨ 新增功能

### 1. 智能回复开关 ⭐
- **功能**: 可以完全关闭AI智能回复，只使用关键词匹配回复
- **位置**: 系统设置页面底部
- **效果**: 
  - ✅ 启用：使用AI服务进行智能回复
  - ❌ 关闭：只使用关键词匹配，不调用AI接口

### 2. AI服务选择
- **智谱AI**: 内置密钥，无需额外配置
- **DeepSeek**: 需要用户提供API密钥

### 3. DeepSeek密钥配置
- **密钥输入**: 支持密码模式保护隐私
- **显示/隐藏**: 可以切换密钥的显示状态
- **连接测试**: 验证配置是否正确

## 📍 界面位置

所有AI设置现在都位于：
```
主界面 → 系统设置页面 → 页面底部的AI配置区域
```

### 控件布局
```
系统设置页面
├── 角色描述 (原有)
├── 通用角色描述 (原有)
├── 保存设置按钮 (原有)
└── AI配置区域 (新增)
    ├── [✓] 启用智能回复
    ├── AI服务: [智谱AI ▼]
    ├── DeepSeek密钥: [••••••••••] [显示]
    └── [测试连接]
```

## 🚀 使用步骤

### 1. 启动程序
```bash
python app.py
```

### 2. 登录系统
- 使用测试账号：admin / 123456
- 或注册新账号

### 3. 进入系统设置
- 在主界面左侧导航栏点击"系统设置"
- 或使用相应的快捷方式

### 4. 配置AI设置
在系统设置页面底部找到AI配置区域：

#### 智能回复开关
- ✅ **启用**：系统会使用AI进行智能回复
- ❌ **关闭**：系统只使用关键词匹配回复，不调用AI接口

#### AI服务选择
- **智谱AI**：选择后无需额外配置
- **DeepSeek**：选择后需要配置API密钥

#### DeepSeek配置（如果选择DeepSeek）
1. 访问 [DeepSeek平台](https://platform.deepseek.com/)
2. 注册账号并获取API密钥
3. 在密钥输入框中输入密钥
4. 点击"显示"按钮可以查看输入的密钥
5. 点击"测试连接"验证配置

### 5. 保存设置
- 点击"保存设置"按钮
- 系统会验证配置并保存
- 如果选择DeepSeek但未设置密钥，会显示警告

## 🔧 配置说明

### 数据库字段
| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| ai_reply_enabled | BOOLEAN | 智能回复开关 | 1 (启用) |
| ai_service | TEXT | AI服务类型 | 'zhipu' |
| deepseek_key | TEXT | DeepSeek API密钥 | '' |

### 配置优先级
1. **智能回复关闭** → 只使用关键词匹配
2. **智能回复启用 + 智谱AI** → 使用智谱AI
3. **智能回复启用 + DeepSeek + 有密钥** → 使用DeepSeek
4. **智能回复启用 + DeepSeek + 无密钥** → 降级到智谱AI

## 📊 工作流程

```mermaid
graph TD
    A[收到客户消息] --> B{智能回复启用?}
    B -->|否| C[关键词匹配]
    B -->|是| D{AI服务类型}
    D -->|智谱AI| E[调用智谱AI]
    D -->|DeepSeek| F{有API密钥?}
    F -->|是| G[调用DeepSeek]
    F -->|否| H[降级到智谱AI]
    C --> I[返回关键词回复或播放提示音]
    E --> J[返回AI回复]
    G --> J
    H --> J
    J --> K[发送回复给客户]
    I --> K
```

## 🧪 测试功能

### 自动测试
```bash
python test_ai_settings_ui.py
```

### 手动测试步骤
1. **测试智能回复开关**
   - 关闭智能回复，发送消息 → 应该只有关键词匹配
   - 启用智能回复，发送消息 → 应该有AI回复

2. **测试AI服务切换**
   - 选择智谱AI → 应该正常工作
   - 选择DeepSeek（无密钥）→ 应该降级到智谱AI
   - 选择DeepSeek（有密钥）→ 应该使用DeepSeek

3. **测试UI交互**
   - 切换AI服务 → DeepSeek选项应该显示/隐藏
   - 点击显示/隐藏按钮 → 密钥应该切换显示状态
   - 点击测试连接 → 应该显示测试结果

## 🔍 调试信息

程序运行时会输出详细的调试信息：

```
[DEBUG] 初始化AI设置 - 服务: zhipu, 智能回复: True
[DEBUG] AI设置控件添加到系统页面成功
[DEBUG] AI设置已加载到UI - 服务: zhipu, 智能回复: True
[DEBUG] 智能回复: 启用, 使用AI服务: zhipu
[DEBUG] 保存智能回复开关: True
[DEBUG] 保存AI服务设置: deepseek
[DEBUG] 保存DeepSeek密钥: 已设置
```

## ⚠️ 注意事项

1. **智能回复关闭时**
   - 系统只使用关键词匹配
   - 没有匹配的关键词时会播放提示音
   - 不会调用任何AI接口

2. **DeepSeek配置**
   - 密钥会以密码形式存储在本地数据库
   - 请妥善保管您的API密钥
   - 建议定期更换密钥

3. **网络连接**
   - DeepSeek需要稳定的网络连接
   - 连接失败时会自动降级到智谱AI

4. **费用控制**
   - DeepSeek是付费服务
   - 关闭智能回复可以完全避免API调用费用

## 🆘 故障排除

### 常见问题

1. **AI设置控件不显示**
   - 检查数据库字段是否正确添加
   - 运行 `python add_db_fields.py`

2. **智能回复开关不生效**
   - 检查调试日志中的开关状态
   - 确认设置已正确保存

3. **DeepSeek连接失败**
   - 检查API密钥是否正确
   - 检查网络连接
   - 查看调试日志中的错误信息

### 重置配置
如果需要重置所有AI配置：
```python
from data.database_manager import DatabaseManager
db = DatabaseManager()
db.update_system_info(ai_reply_enabled=True, ai_service='zhipu', deepseek_key='')
```

## 🎯 使用建议

1. **新用户**：建议先使用智谱AI，熟悉功能后再考虑DeepSeek
2. **成本控制**：可以关闭智能回复，只使用关键词匹配
3. **测试环境**：使用测试连接功能验证配置
4. **生产环境**：定期检查API密钥的有效性和余额

## 📈 性能优化

- 智能回复关闭时，系统响应更快
- 关键词匹配比AI调用速度更快
- 可以根据业务需求灵活切换模式
