#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加数据库字段的脚本
"""

import sqlite3

def add_database_fields():
    """添加AI服务选择和DeepSeek密钥字段"""
    try:
        conn = sqlite3.connect('app_data.db')
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(system_info)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加ai_service字段
        if 'ai_service' not in columns:
            cursor.execute('ALTER TABLE system_info ADD COLUMN ai_service TEXT DEFAULT "zhipu"')
            print("✅ 添加ai_service字段成功")
        else:
            print("ℹ️ ai_service字段已存在")
        
        # 添加deepseek_key字段
        if 'deepseek_key' not in columns:
            cursor.execute('ALTER TABLE system_info ADD COLUMN deepseek_key TEXT DEFAULT ""')
            print("✅ 添加deepseek_key字段成功")
        else:
            print("ℹ️ deepseek_key字段已存在")

        # 添加ai_reply_enabled字段（智能回复开关）
        if 'ai_reply_enabled' not in columns:
            cursor.execute('ALTER TABLE system_info ADD COLUMN ai_reply_enabled BOOLEAN DEFAULT 1')
            print("✅ 添加ai_reply_enabled字段成功")
        else:
            print("ℹ️ ai_reply_enabled字段已存在")
        
        conn.commit()
        conn.close()
        
        print("🎉 数据库字段添加完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {str(e)}")
        return False

if __name__ == "__main__":
    add_database_fields()
