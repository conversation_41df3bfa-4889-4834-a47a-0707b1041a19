#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的端口检查脚本
"""

import socket

def check_port(host, port):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"检查端口 {port} 时出错: {e}")
        return False

def main():
    print("端口检查工具")
    print("=" * 30)
    
    ports_to_check = [443, 5000, 8765]
    
    for port in ports_to_check:
        is_open = check_port('localhost', port)
        status = "✅ 开放" if is_open else "❌ 关闭"
        print(f"端口 {port}: {status}")

if __name__ == "__main__":
    main()
