#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千牛连接问题诊断脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import socket
import requests
import time
from data.database_manager import DatabaseManager

def check_services():
    """检查服务状态"""
    print("🔍 检查服务状态")
    print("=" * 50)
    
    services = {
        'WebSocket服务器 (8765)': ('localhost', 8765),
        'Flask服务器 (443)': ('localhost', 443),
        'Flask服务器 (5000)': ('localhost', 5000)
    }
    
    results = {}
    
    for service_name, (host, port) in services.items():
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ {service_name}: 运行中")
                results[service_name] = True
            else:
                print(f"❌ {service_name}: 未运行")
                results[service_name] = False
        except Exception as e:
            print(f"❌ {service_name}: 检查失败 - {e}")
            results[service_name] = False
    
    return results

def check_injection_script():
    """检查注入脚本"""
    print("\n🔍 检查注入脚本")
    print("=" * 50)
    
    try:
        # 检查注入脚本是否可访问
        urls_to_check = [
            'https://localhost/imsupport',
            'https://127.0.0.1/imsupport',
            'https://localhost:443/imsupport'
        ]
        
        for url in urls_to_check:
            try:
                response = requests.get(url, timeout=5, verify=False)
                if response.status_code == 200:
                    print(f"✅ 注入脚本可访问: {url}")
                    print(f"   脚本大小: {len(response.text)} 字符")
                    return True
                else:
                    print(f"⚠️ {url}: HTTP {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"❌ {url}: 连接失败")
            except Exception as e:
                print(f"❌ {url}: {str(e)}")
        
        print("❌ 所有注入脚本URL都无法访问")
        return False
        
    except Exception as e:
        print(f"❌ 检查注入脚本失败: {str(e)}")
        return False

def check_ai_settings():
    """检查AI设置"""
    print("\n🔍 检查AI设置")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        system_info = db.get_system_info()
        
        # 检查智能回复设置
        ai_reply_enabled = system_info[14] if len(system_info) > 14 else True
        ai_service = system_info[12] if len(system_info) > 12 else 'zhipu'
        deepseek_key = system_info[13] if len(system_info) > 13 else ''
        
        print(f"智能回复开关: {'✅ 启用' if ai_reply_enabled else '❌ 关闭'}")
        print(f"AI服务类型: {ai_service}")
        print(f"DeepSeek密钥: {'✅ 已设置' if deepseek_key else '❌ 未设置'}")
        
        if not ai_reply_enabled:
            print("\n⚠️ 智能回复已关闭，系统应该使用关键词匹配")
            
            # 检查关键词数据
            try:
                # 这里可以添加检查关键词数据库的代码
                print("📋 关键词匹配功能检查:")
                print("   - 需要检查关键词数据库是否有数据")
                print("   - 需要检查关键词匹配逻辑是否正常")
            except Exception as e:
                print(f"❌ 关键词检查失败: {e}")
        
        return {
            'ai_reply_enabled': ai_reply_enabled,
            'ai_service': ai_service,
            'deepseek_key_set': bool(deepseek_key)
        }
        
    except Exception as e:
        print(f"❌ 检查AI设置失败: {str(e)}")
        return None

def check_websocket_connection():
    """测试WebSocket连接"""
    print("\n🔍 测试WebSocket连接")
    print("=" * 50)
    
    try:
        import websocket
        
        connection_result = {'connected': False, 'error': None}
        
        def on_open(ws):
            print("✅ WebSocket连接成功建立")
            connection_result['connected'] = True
            ws.send('{"type": "test", "message": "连接测试"}')
            
        def on_message(ws, message):
            print(f"📨 收到WebSocket消息: {message}")
            ws.close()
            
        def on_error(ws, error):
            print(f"❌ WebSocket连接错误: {error}")
            connection_result['error'] = str(error)
            
        def on_close(ws, close_status_code, close_msg):
            print("🔌 WebSocket连接已关闭")
        
        print("🔄 尝试连接WebSocket服务器...")
        ws = websocket.WebSocketApp("ws://localhost:8765",
                                  on_open=on_open,
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close)
        
        # 设置超时运行
        ws.run_forever(ping_timeout=3, ping_interval=1)
        
        return connection_result['connected']
        
    except ImportError:
        print("⚠️ websocket-client 模块未安装")
        print("   可以运行: pip install websocket-client")
        return False
    except Exception as e:
        print(f"❌ WebSocket测试失败: {str(e)}")
        return False

def diagnose_connection_flow():
    """诊断连接流程"""
    print("\n🔍 诊断连接流程")
    print("=" * 50)
    
    print("千牛连接的完整流程应该是:")
    print("1. 程序启动 → Flask和WebSocket服务器启动")
    print("2. 点击连接千牛 → 千牛窗口刷新")
    print("3. 千牛页面加载 → 请求注入脚本 (https://localhost/imsupport)")
    print("4. 注入脚本执行 → 建立WebSocket连接")
    print("5. WebSocket连接成功 → 状态显示'已连接'")
    print("6. 收到消息 → 触发自动回复逻辑")
    
    print("\n可能的问题点:")
    print("❓ 千牛页面是否成功请求了注入脚本？")
    print("❓ 注入脚本是否成功执行？")
    print("❓ WebSocket连接是否建立？")
    print("❓ 消息拦截是否正常工作？")

def main():
    print("千牛连接问题诊断工具")
    print("=" * 50)
    
    # 1. 检查服务状态
    service_results = check_services()
    
    # 2. 检查注入脚本
    script_accessible = check_injection_script()
    
    # 3. 检查AI设置
    ai_settings = check_ai_settings()
    
    # 4. 测试WebSocket连接
    websocket_ok = check_websocket_connection()
    
    # 5. 诊断连接流程
    diagnose_connection_flow()
    
    # 6. 总结和建议
    print("\n" + "=" * 50)
    print("📊 诊断结果总结")
    print("=" * 50)
    
    issues = []
    
    if not service_results.get('WebSocket服务器 (8765)', False):
        issues.append("WebSocket服务器未运行")
    
    if not any(service_results.get(k, False) for k in service_results if 'Flask' in k):
        issues.append("Flask服务器未运行")
    
    if not script_accessible:
        issues.append("注入脚本无法访问")
    
    if not websocket_ok:
        issues.append("WebSocket连接测试失败")
    
    if ai_settings and not ai_settings['ai_reply_enabled']:
        print("ℹ️ 智能回复已关闭，应该使用关键词匹配")
    
    if issues:
        print("❌ 发现以下问题:")
        for issue in issues:
            print(f"   - {issue}")
        
        print("\n🔧 建议解决方案:")
        print("1. 确保主程序 (python app.py) 正在运行")
        print("2. 检查程序启动日志是否有错误")
        print("3. 确认千牛已启动并完全加载")
        print("4. 尝试重新启动程序")
        print("5. 检查防火墙是否阻止了端口访问")
    else:
        print("✅ 所有基础服务都正常")
        print("\n如果仍然无法自动回复，可能的原因:")
        print("1. 千牛页面没有正确加载注入脚本")
        print("2. 消息拦截逻辑有问题")
        print("3. 关键词匹配数据库为空")

if __name__ == "__main__":
    main()
