#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的千牛连接诊断
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import socket
from data.database_manager import DatabaseManager

def check_ports():
    """检查端口状态"""
    print("🔍 检查端口状态")
    print("-" * 30)
    
    ports = [443, 5000, 8765]
    results = {}
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"✅ 端口 {port}: 开放")
                results[port] = True
            else:
                print(f"❌ 端口 {port}: 关闭")
                results[port] = False
        except Exception as e:
            print(f"❌ 端口 {port}: 检查失败 - {e}")
            results[port] = False
    
    return results

def check_ai_settings():
    """检查AI设置"""
    print("\n🔍 检查AI设置")
    print("-" * 30)
    
    try:
        db = DatabaseManager()
        system_info = db.get_system_info()
        
        ai_reply_enabled = system_info[14] if len(system_info) > 14 else True
        ai_service = system_info[12] if len(system_info) > 12 else 'zhipu'
        
        print(f"智能回复: {'启用' if ai_reply_enabled else '关闭'}")
        print(f"AI服务: {ai_service}")
        
        return ai_reply_enabled
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

def main():
    print("千牛连接简化诊断")
    print("=" * 30)
    
    # 检查端口
    port_results = check_ports()
    
    # 检查AI设置
    ai_enabled = check_ai_settings()
    
    print("\n📊 诊断结果")
    print("-" * 30)
    
    if port_results.get(8765, False):
        print("✅ WebSocket服务器正常")
    else:
        print("❌ WebSocket服务器未运行")
    
    if port_results.get(443, False) or port_results.get(5000, False):
        print("✅ Flask服务器正常")
    else:
        print("❌ Flask服务器未运行")
    
    if ai_enabled is False:
        print("ℹ️ 智能回复已关闭，应使用关键词匹配")
    elif ai_enabled is True:
        print("ℹ️ 智能回复已启用")
    
    print("\n💡 问题分析")
    print("-" * 30)
    
    if not port_results.get(8765, False):
        print("❌ 主要问题：WebSocket服务器未启动")
        print("   这会导致千牛状态显示'未连接'")
        print("   解决方案：重新启动主程序")
    elif ai_enabled is False:
        print("ℹ️ 智能回复已关闭")
        print("   系统应该使用关键词匹配进行回复")
        print("   如果没有回复，可能是关键词数据库为空")
    else:
        print("✅ 基础服务正常")
        print("   如果仍无法回复，可能是:")
        print("   1. 千牛页面未加载注入脚本")
        print("   2. WebSocket连接未建立")
        print("   3. 消息拦截逻辑有问题")

if __name__ == "__main__":
    main()
