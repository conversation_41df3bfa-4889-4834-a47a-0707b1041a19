# -*- coding: utf-8 -*-
"""
AI设置对话框
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QComboBox, QLineEdit, QPushButton, QTextEdit,
                               QGroupBox, QFormLayout, QMessageBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

class AISettingsDialog(QDialog):
    """AI设置对话框"""
    
    settings_saved = Signal()  # 设置保存信号
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db = db_manager
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("AI服务设置")
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("AI服务配置")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # AI服务选择组
        service_group = QGroupBox("AI服务选择")
        service_layout = QFormLayout()
        
        self.ai_service_combo = QComboBox()
        self.ai_service_combo.addItem("智谱AI (ZhipuAI)", "zhipu")
        self.ai_service_combo.addItem("DeepSeek", "deepseek")
        self.ai_service_combo.currentTextChanged.connect(self.on_service_changed)
        
        service_layout.addRow("AI服务:", self.ai_service_combo)
        service_group.setLayout(service_layout)
        main_layout.addWidget(service_group)
        
        # 智谱AI设置组
        self.zhipu_group = QGroupBox("智谱AI设置")
        zhipu_layout = QFormLayout()
        
        zhipu_info = QLabel("智谱AI已内置密钥，无需额外配置")
        zhipu_info.setStyleSheet("color: #666; font-style: italic;")
        zhipu_layout.addRow(zhipu_info)
        
        self.zhipu_group.setLayout(zhipu_layout)
        main_layout.addWidget(self.zhipu_group)
        
        # DeepSeek设置组
        self.deepseek_group = QGroupBox("DeepSeek设置")
        deepseek_layout = QFormLayout()
        
        self.deepseek_key_edit = QLineEdit()
        self.deepseek_key_edit.setPlaceholderText("请输入DeepSeek API密钥")
        self.deepseek_key_edit.setEchoMode(QLineEdit.Password)
        
        # 显示/隐藏密钥按钮
        key_layout = QHBoxLayout()
        key_layout.addWidget(self.deepseek_key_edit)
        
        self.show_key_btn = QPushButton("显示")
        self.show_key_btn.setFixedWidth(60)
        self.show_key_btn.clicked.connect(self.toggle_key_visibility)
        key_layout.addWidget(self.show_key_btn)
        
        deepseek_layout.addRow("API密钥:", key_layout)
        
        # DeepSeek使用说明
        deepseek_info = QTextEdit()
        deepseek_info.setMaximumHeight(120)
        deepseek_info.setReadOnly(True)
        deepseek_info.setText("""DeepSeek使用说明：
1. 访问 https://platform.deepseek.com/ 注册账号
2. 在控制台创建API密钥
3. 将密钥粘贴到上方输入框
4. DeepSeek提供高质量的AI对话服务
5. 支持中文对话，适合客服场景""")
        deepseek_info.setStyleSheet("background-color: #f5f5f5; border: 1px solid #ddd; padding: 8px;")
        
        deepseek_layout.addRow("使用说明:", deepseek_info)
        self.deepseek_group.setLayout(deepseek_layout)
        main_layout.addWidget(self.deepseek_group)
        
        # 测试连接按钮
        self.test_btn = QPushButton("测试连接")
        self.test_btn.clicked.connect(self.test_connection)
        main_layout.addWidget(self.test_btn)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("保存设置")
        self.save_btn.clicked.connect(self.save_settings)
        self.save_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #45a049; }")
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #da190b; }")
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
        
        # 初始状态
        self.on_service_changed()
    
    def load_settings(self):
        """加载当前设置"""
        try:
            system_info = self.db.get_system_info()
            
            # 设置AI服务选择
            ai_service = system_info[12] if len(system_info) > 12 and system_info[12] else 'zhipu'
            index = 0 if ai_service == 'zhipu' else 1
            self.ai_service_combo.setCurrentIndex(index)
            
            # 设置DeepSeek密钥
            deepseek_key = system_info[13] if len(system_info) > 13 and system_info[13] else ''
            self.deepseek_key_edit.setText(deepseek_key)
            
        except Exception as e:
            print(f"[ERROR] 加载AI设置失败: {str(e)}")
    
    def on_service_changed(self):
        """AI服务选择改变时的处理"""
        current_service = self.ai_service_combo.currentData()
        
        if current_service == 'zhipu':
            self.zhipu_group.setVisible(True)
            self.deepseek_group.setVisible(False)
        else:
            self.zhipu_group.setVisible(False)
            self.deepseek_group.setVisible(True)
    
    def toggle_key_visibility(self):
        """切换密钥显示/隐藏"""
        if self.deepseek_key_edit.echoMode() == QLineEdit.Password:
            self.deepseek_key_edit.setEchoMode(QLineEdit.Normal)
            self.show_key_btn.setText("隐藏")
        else:
            self.deepseek_key_edit.setEchoMode(QLineEdit.Password)
            self.show_key_btn.setText("显示")
    
    def test_connection(self):
        """测试AI服务连接"""
        current_service = self.ai_service_combo.currentData()
        
        if current_service == 'zhipu':
            QMessageBox.information(self, "测试结果", "智谱AI连接正常")
        elif current_service == 'deepseek':
            api_key = self.deepseek_key_edit.text().strip()
            if not api_key:
                QMessageBox.warning(self, "测试失败", "请先输入DeepSeek API密钥")
                return
            
            # 这里可以添加实际的连接测试逻辑
            try:
                # 简单的API测试
                import requests
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                # 这里可以发送一个简单的测试请求
                QMessageBox.information(self, "测试结果", "DeepSeek连接测试完成\n(实际连接将在使用时验证)")
            except Exception as e:
                QMessageBox.warning(self, "测试失败", f"连接测试失败: {str(e)}")
    
    def save_settings(self):
        """保存设置"""
        try:
            ai_service = self.ai_service_combo.currentData()
            deepseek_key = self.deepseek_key_edit.text().strip()
            
            # 验证设置
            if ai_service == 'deepseek' and not deepseek_key:
                QMessageBox.warning(self, "设置错误", "使用DeepSeek服务时必须提供API密钥")
                return
            
            # 保存到数据库
            self.db.update_system_info(ai_service=ai_service)
            self.db.update_system_info(deepseek_key=deepseek_key)
            
            QMessageBox.information(self, "保存成功", "AI服务设置已保存")
            self.settings_saved.emit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存设置时出错: {str(e)}")
