#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI集成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.database_manager import DatabaseManager
from src.Message import Message

def test_ai_integration():
    """测试AI集成功能"""
    print("🧪 测试AI集成功能")
    print("=" * 50)
    
    try:
        # 初始化数据库管理器
        db = DatabaseManager()
        
        # 检查数据库字段
        system_info = db.get_system_info()
        print(f"📊 系统信息字段数量: {len(system_info)}")
        
        if len(system_info) >= 14:
            ai_service = system_info[12] if system_info[12] else 'zhipu'
            deepseek_key = system_info[13] if system_info[13] else ''
            
            print(f"🤖 当前AI服务: {ai_service}")
            print(f"🔑 DeepSeek密钥: {'已配置' if deepseek_key else '未配置'}")
        else:
            print("⚠️ 数据库字段不完整，可能需要重新添加字段")
            return False
        
        # 测试Message类
        message = Message(db)
        print("✅ Message类初始化成功")
        
        # 测试AI服务调用方法
        if hasattr(message, 'call_ai_service'):
            print("✅ call_ai_service方法存在")
        else:
            print("❌ call_ai_service方法不存在")
            return False
        
        if hasattr(message, 'deepseek_message'):
            print("✅ deepseek_message方法存在")
        else:
            print("❌ deepseek_message方法不存在")
            return False
        
        # 测试数据结构
        test_data = {
            "ccode": "test_session",
            "message": "你好，这是一个测试消息",
            "knowledge": "这是测试知识库内容"
        }
        
        print("\n🔄 测试AI服务调用...")
        
        # 测试智谱AI调用
        try:
            result = message.call_ai_service(test_data, "你是一个测试助手", system_info)
            print("✅ AI服务调用成功")
            print(f"📝 返回结果类型: {type(result)}")
        except Exception as e:
            print(f"⚠️ AI服务调用测试失败: {str(e)}")
        
        print("\n🎉 AI集成功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_database_fields():
    """测试数据库字段"""
    print("\n📊 测试数据库字段")
    print("-" * 30)
    
    try:
        import sqlite3
        conn = sqlite3.connect('app_data.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(system_info)")
        columns = cursor.fetchall()
        
        print("数据库字段:")
        for col in columns:
            print(f"  {col[0]}: {col[1]} ({col[2]})")
        
        # 检查必要字段
        column_names = [col[1] for col in columns]
        required_fields = ['ai_service', 'deepseek_key']
        
        missing_fields = [field for field in required_fields if field not in column_names]
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
            return False
        else:
            print("✅ 所有必要字段都存在")
        
        # 获取当前数据
        cursor.execute("SELECT ai_service, deepseek_key FROM system_info WHERE id = 1")
        result = cursor.fetchone()
        
        if result:
            ai_service, deepseek_key = result
            print(f"当前AI服务: {ai_service or '未设置'}")
            print(f"DeepSeek密钥: {'已设置' if deepseek_key else '未设置'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")
        return False

def update_ai_service_for_test():
    """更新AI服务设置用于测试"""
    print("\n⚙️ 更新AI服务设置")
    print("-" * 30)
    
    try:
        db = DatabaseManager()
        
        # 设置为DeepSeek服务（用于测试）
        db.update_system_info(ai_service='deepseek')
        print("✅ AI服务设置为DeepSeek")
        
        # 可以设置一个测试密钥（如果有的话）
        test_key = input("请输入DeepSeek API密钥进行测试（可选，直接回车跳过）: ").strip()
        if test_key:
            db.update_system_info(deepseek_key=test_key)
            print("✅ DeepSeek密钥已设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新设置失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("AI集成功能测试工具")
    print("=" * 50)
    
    # 测试数据库字段
    if not test_database_fields():
        print("❌ 数据库字段测试失败，请先运行 add_db_fields.py")
        sys.exit(1)
    
    # 测试AI集成
    if not test_ai_integration():
        print("❌ AI集成测试失败")
        sys.exit(1)
    
    # 询问是否要更新设置
    choice = input("\n是否要更新AI服务设置进行测试？(y/n): ").strip().lower()
    if choice == 'y':
        update_ai_service_for_test()
    
    print("\n🎉 所有测试完成！")
    print("\n📋 使用说明:")
    print("1. 启动程序后，在主界面可以看到'AI设置'按钮")
    print("2. 点击按钮打开AI设置对话框")
    print("3. 选择AI服务（智谱AI或DeepSeek）")
    print("4. 如果选择DeepSeek，需要输入API密钥")
    print("5. 保存设置后，系统将使用选择的AI服务进行自动回复")
