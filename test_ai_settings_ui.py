#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI设置UI集成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.database_manager import DatabaseManager
from src.Message import Message

def test_database_fields():
    """测试数据库字段"""
    print("🔍 测试数据库字段")
    print("=" * 50)
    
    try:
        import sqlite3
        conn = sqlite3.connect('app_data.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(system_info)")
        columns = cursor.fetchall()
        
        print("数据库字段:")
        for col in columns:
            print(f"  {col[0]}: {col[1]} ({col[2]})")
        
        # 检查必要字段
        column_names = [col[1] for col in columns]
        required_fields = ['ai_service', 'deepseek_key', 'ai_reply_enabled']
        
        missing_fields = [field for field in required_fields if field not in column_names]
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
            return False
        else:
            print("✅ 所有必要字段都存在")
        
        # 获取当前数据
        cursor.execute("SELECT ai_service, deepseek_key, ai_reply_enabled FROM system_info WHERE id = 1")
        result = cursor.fetchone()
        
        if result:
            ai_service, deepseek_key, ai_reply_enabled = result
            print(f"当前AI服务: {ai_service or '未设置'}")
            print(f"DeepSeek密钥: {'已设置' if deepseek_key else '未设置'}")
            print(f"智能回复开关: {'启用' if ai_reply_enabled else '关闭'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")
        return False

def test_ai_reply_switch():
    """测试智能回复开关功能"""
    print("\n🔄 测试智能回复开关功能")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        message = Message(db)
        
        # 获取系统信息
        system_info = db.get_system_info()
        print(f"系统信息字段数量: {len(system_info)}")
        
        # 测试数据
        test_data = {
            "ccode": "test_session",
            "message": "你好，这是一个测试消息",
            "knowledge": "这是测试知识库内容"
        }
        
        # 测试智能回复启用状态
        print("\n📋 测试1: 智能回复启用状态")
        db.update_system_info(ai_reply_enabled=True)
        system_info = db.get_system_info()
        
        result = message.call_ai_service(test_data, "你是一个测试助手", system_info)
        if result is not None:
            print("✅ 智能回复启用时，AI服务正常调用")
        else:
            print("❌ 智能回复启用时，AI服务调用失败")
        
        # 测试智能回复关闭状态
        print("\n📋 测试2: 智能回复关闭状态")
        db.update_system_info(ai_reply_enabled=False)
        system_info = db.get_system_info()
        
        result = message.call_ai_service(test_data, "你是一个测试助手", system_info)
        if result is None:
            print("✅ 智能回复关闭时，AI服务正确跳过")
        else:
            print("❌ 智能回复关闭时，AI服务未正确跳过")
        
        # 恢复默认设置
        db.update_system_info(ai_reply_enabled=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 智能回复开关测试失败: {str(e)}")
        return False

def test_ai_service_selection():
    """测试AI服务选择功能"""
    print("\n🤖 测试AI服务选择功能")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        message = Message(db)
        
        # 测试数据
        test_data = {
            "ccode": "test_session",
            "message": "你好，这是一个测试消息",
            "knowledge": "这是测试知识库内容"
        }
        
        # 测试智谱AI服务
        print("\n📋 测试1: 智谱AI服务")
        db.update_system_info(ai_service='zhipu', ai_reply_enabled=True)
        system_info = db.get_system_info()
        
        result = message.call_ai_service(test_data, "你是一个测试助手", system_info)
        if result is not None:
            print("✅ 智谱AI服务调用成功")
        else:
            print("❌ 智谱AI服务调用失败")
        
        # 测试DeepSeek服务（无密钥）
        print("\n📋 测试2: DeepSeek服务（无密钥）")
        db.update_system_info(ai_service='deepseek', deepseek_key='', ai_reply_enabled=True)
        system_info = db.get_system_info()
        
        result = message.call_ai_service(test_data, "你是一个测试助手", system_info)
        if result is not None:
            print("✅ DeepSeek无密钥时，正确降级到智谱AI")
        else:
            print("❌ DeepSeek无密钥时，降级失败")
        
        # 恢复默认设置
        db.update_system_info(ai_service='zhipu')
        
        return True
        
    except Exception as e:
        print(f"❌ AI服务选择测试失败: {str(e)}")
        return False

def interactive_test():
    """交互式测试"""
    print("\n🎮 交互式测试")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        print("当前可以进行以下测试:")
        print("1. 设置智能回复开关")
        print("2. 设置AI服务类型")
        print("3. 设置DeepSeek密钥")
        print("4. 查看当前设置")
        
        while True:
            choice = input("\n请选择操作 (1-4, q退出): ").strip()
            
            if choice == 'q':
                break
            elif choice == '1':
                enabled = input("启用智能回复? (y/n): ").strip().lower() == 'y'
                db.update_system_info(ai_reply_enabled=enabled)
                print(f"✅ 智能回复已{'启用' if enabled else '关闭'}")
                
            elif choice == '2':
                service = input("选择AI服务 (zhipu/deepseek): ").strip()
                if service in ['zhipu', 'deepseek']:
                    db.update_system_info(ai_service=service)
                    print(f"✅ AI服务已设置为: {service}")
                else:
                    print("❌ 无效的AI服务类型")
                    
            elif choice == '3':
                key = input("输入DeepSeek API密钥 (留空清除): ").strip()
                db.update_system_info(deepseek_key=key)
                print(f"✅ DeepSeek密钥已{'设置' if key else '清除'}")
                
            elif choice == '4':
                system_info = db.get_system_info()
                ai_service = system_info[12] if len(system_info) > 12 else 'zhipu'
                deepseek_key = system_info[13] if len(system_info) > 13 else ''
                ai_reply_enabled = system_info[14] if len(system_info) > 14 else True
                
                print("\n📊 当前设置:")
                print(f"  AI服务: {ai_service}")
                print(f"  DeepSeek密钥: {'已设置' if deepseek_key else '未设置'}")
                print(f"  智能回复: {'启用' if ai_reply_enabled else '关闭'}")
            else:
                print("❌ 无效选择")
        
        return True
        
    except Exception as e:
        print(f"❌ 交互式测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("AI设置UI集成测试工具")
    print("=" * 50)
    
    # 测试数据库字段
    if not test_database_fields():
        print("❌ 数据库字段测试失败，请先运行 add_db_fields.py")
        sys.exit(1)
    
    # 测试智能回复开关
    if not test_ai_reply_switch():
        print("❌ 智能回复开关测试失败")
        sys.exit(1)
    
    # 测试AI服务选择
    if not test_ai_service_selection():
        print("❌ AI服务选择测试失败")
        sys.exit(1)
    
    print("\n🎉 所有自动测试通过！")
    
    # 询问是否进行交互式测试
    choice = input("\n是否进行交互式测试？(y/n): ").strip().lower()
    if choice == 'y':
        interactive_test()
    
    print("\n📋 使用说明:")
    print("1. 启动程序: python app.py")
    print("2. 登录后进入系统设置页面")
    print("3. 在页面底部可以看到AI配置选项:")
    print("   - 智能回复开关：控制是否使用AI自动回复")
    print("   - AI服务选择：选择智谱AI或DeepSeek")
    print("   - DeepSeek密钥：配置DeepSeek API密钥")
    print("4. 点击'保存设置'按钮保存配置")
    print("5. 当智能回复关闭时，系统只使用关键词匹配回复")
