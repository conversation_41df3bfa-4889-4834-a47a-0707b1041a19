#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动发送功能的脚本
"""

import win32gui
import win32con
import win32api
import win32process
import time

def find_qianniu_windows():
    """查找所有可能的千牛窗口"""
    windows = []
    
    def enum_windows_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            if ("千牛" in window_text or 
                "taobao" in window_text.lower() or 
                "aliim" in window_text.lower() or
                class_name == "Qt5152QWindowIcon"):
                windows.append({
                    'hwnd': hwnd,
                    'title': window_text,
                    'class': class_name,
                    'visible': win32gui.IsWindowVisible(hwnd),
                    'minimized': win32gui.IsIconic(hwnd)
                })
        return True
    
    win32gui.EnumWindows(enum_windows_callback, windows)
    return windows

def test_send_message():
    """测试发送消息功能"""
    print("=== 千牛窗口检测测试 ===")
    
    # 查找所有可能的千牛窗口
    windows = find_qianniu_windows()
    
    if not windows:
        print("❌ 未找到任何千牛相关窗口")
        print("请确保千牛已经启动并且登录")
        return False
    
    print(f"✅ 找到 {len(windows)} 个可能的千牛窗口:")
    for i, window in enumerate(windows):
        print(f"  {i+1}. 标题: {window['title']}")
        print(f"     类名: {window['class']}")
        print(f"     句柄: {window['hwnd']}")
        print(f"     可见: {window['visible']}")
        print(f"     最小化: {window['minimized']}")
        print()
    
    # 尝试对第一个窗口进行操作
    target_window = windows[0]
    hwnd = target_window['hwnd']
    
    print(f"=== 尝试操作窗口: {target_window['title']} ===")
    
    try:
        # 如果窗口最小化，恢复窗口
        if win32gui.IsIconic(hwnd):
            print("窗口已最小化，正在恢复...")
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            time.sleep(0.5)
        
        # 获取当前焦点窗口
        current_focus = win32gui.GetForegroundWindow()
        print(f"当前焦点窗口: {current_focus}")
        
        # 尝试将焦点设置到千牛窗口
        current_thread = win32api.GetCurrentThreadId()
        target_thread = win32process.GetWindowThreadProcessId(hwnd)[0]
        
        print(f"当前线程ID: {current_thread}")
        print(f"目标线程ID: {target_thread}")
        
        # 附加线程输入状态
        win32process.AttachThreadInput(current_thread, target_thread, True)
        
        # 将窗口带到前台
        win32gui.SetForegroundWindow(hwnd)
        win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
        win32gui.SetFocus(hwnd)
        
        # 解除线程输入状态附加
        win32process.AttachThreadInput(current_thread, target_thread, False)
        
        # 等待窗口获得焦点
        time.sleep(0.5)
        
        # 检查窗口是否真的获得了焦点
        new_focus = win32gui.GetForegroundWindow()
        print(f"操作后焦点窗口: {new_focus}")
        
        if new_focus == hwnd:
            print("✅ 成功获取窗口焦点")
            
            # 模拟发送回车键
            print("正在发送回车键...")
            win32api.keybd_event(win32con.VK_RETURN, 0, 0, 0)  # 按下
            time.sleep(0.05)
            win32api.keybd_event(win32con.VK_RETURN, 0, win32con.KEYEVENTF_KEYUP, 0)  # 释放
            
            print("✅ 回车键已发送")
            return True
        else:
            print("❌ 无法获取窗口焦点")
            return False
            
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("千牛自动发送功能测试")
    print("=" * 50)
    
    result = test_send_message()
    
    print("\n" + "=" * 50)
    if result:
        print("✅ 测试通过：自动发送功能正常")
    else:
        print("❌ 测试失败：自动发送功能异常")
        print("\n可能的解决方案：")
        print("1. 确保千牛已经启动并登录")
        print("2. 确保千牛窗口没有被其他程序阻挡")
        print("3. 尝试手动点击千牛窗口使其获得焦点")
        print("4. 检查是否有安全软件阻止了自动化操作")
