#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连接千牛按钮点击功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import win32gui
import win32con
import time
import pyautogui

def test_qianniu_connection_directly():
    """直接测试千牛连接功能"""
    print("🔧 直接测试千牛连接功能")
    print("=" * 50)
    
    try:
        print("[DEBUG] 开始连接千牛...")
        
        # 首先获取父窗口句柄
        parent_hwnd = win32gui.FindWindow("Qt5152QWindowIcon", "千牛接待台")
        print(f"[DEBUG] 查找千牛接待台窗口，句柄: {parent_hwnd}")
        
        if not parent_hwnd:
            print("[DEBUG] 未找到千牛接待台窗口，尝试启动千牛...")
            try:
                os.startfile("aliim:login")
                print("[DEBUG] 已发送千牛启动命令")
                
                # 等待千牛启动
                for i in range(10):
                    time.sleep(1)
                    parent_hwnd = win32gui.FindWindow("Qt5152QWindowIcon", "千牛接待台")
                    if parent_hwnd:
                        print(f"[DEBUG] 千牛启动成功，窗口句柄: {parent_hwnd}")
                        break
                    print(f"[DEBUG] 等待千牛启动... ({i+1}/10)")
                
                if not parent_hwnd:
                    print("[ERROR] 千牛启动超时或失败")
                    return False
            except Exception as e:
                print(f"[ERROR] 启动千牛失败: {str(e)}")
                return False
        
        # 显示窗口
        print("[DEBUG] 显示千牛窗口...")
        win32gui.ShowWindow(parent_hwnd, win32con.SW_SHOWMAXIMIZED)
        time.sleep(0.5)
        
        # 查找子窗口
        print("[DEBUG] 查找千牛工作台子窗口...")
        child_hwnd = find_child_window(parent_hwnd, "千牛工作台", 2)
        print(f"[DEBUG] 千牛工作台子窗口句柄: {child_hwnd}")
        
        if child_hwnd:
            rect = win32gui.GetWindowRect(child_hwnd)
            x = rect[0]
            y = rect[1]
            click_x = x + 10
            click_y = y + 10
            print(f"[DEBUG] 点击位置: ({click_x}, {click_y})")
            
            # 使用 pyautogui 移动鼠标并点击
            pyautogui.click(click_x, click_y)
            time.sleep(0.5)
            
            # 模拟点击F5
            print("[DEBUG] 发送F5刷新")
            pyautogui.hotkey('f5')
            
            print("[DEBUG] 千牛连接操作完成")
            return True
        else:
            print("[ERROR] 未找到千牛工作台子窗口")
            return False
            
    except Exception as e:
        print(f"[ERROR] 连接千牛时发生异常: {str(e)}")
        return False

def find_child_window(parent_handle, child_window_title, index=1):
    """查找子窗口"""
    def callback(hwnd, hwnds):
        if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
            hwnds.append(hwnd)
        return True

    hwnds = []
    win32gui.EnumChildWindows(parent_handle, callback, hwnds)

    matched_hwnds = []
    for hwnd in hwnds:
        if win32gui.GetWindowText(hwnd) == child_window_title:
            matched_hwnds.append(hwnd)
        child_hwnd = find_child_window(hwnd, child_window_title, index)
        if child_hwnd:
            matched_hwnds.append(child_hwnd)

    if len(matched_hwnds) >= index:
        return matched_hwnds[len(matched_hwnds)-1]
    return None

def check_websocket_server():
    """检查WebSocket服务器状态"""
    print("\n🌐 检查WebSocket服务器状态")
    print("-" * 30)
    
    try:
        import socket
        
        # 检查端口8765是否被占用
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 8765))
        sock.close()
        
        if result == 0:
            print("✅ WebSocket服务器 (端口8765) 正在运行")
            return True
        else:
            print("❌ WebSocket服务器 (端口8765) 未运行")
            return False
            
    except Exception as e:
        print(f"❌ 检查WebSocket服务器失败: {str(e)}")
        return False

def check_flask_server():
    """检查Flask服务器状态"""
    print("\n🌍 检查Flask服务器状态")
    print("-" * 30)

    # 检查多个可能的端口
    ports_to_check = [443, 5000]

    for port in ports_to_check:
        try:
            import requests

            # 根据端口选择协议
            protocol = "https" if port == 443 else "http"
            url = f"{protocol}://localhost:{port}"

            # 对于HTTPS，禁用SSL验证
            verify_ssl = False if port == 443 else True

            response = requests.get(url, timeout=2, verify=verify_ssl)
            if response.status_code == 200:
                print(f"✅ Flask服务器 (端口{port}) 正在运行")
                return True
            else:
                print(f"⚠️ Flask服务器 (端口{port}) 响应异常: {response.status_code}")

        except requests.exceptions.ConnectionError:
            print(f"❌ Flask服务器 (端口{port}) 未运行")
        except Exception as e:
            print(f"❌ 检查Flask服务器 (端口{port}) 失败: {str(e)}")

    return False

def main():
    print("连接千牛按钮功能测试")
    print("=" * 50)
    
    # 1. 测试千牛连接功能
    print("1️⃣ 测试千牛连接功能")
    qianniu_success = test_qianniu_connection_directly()
    
    # 2. 检查WebSocket服务器
    print("\n2️⃣ 检查服务器状态")
    websocket_running = check_websocket_server()
    flask_running = check_flask_server()
    
    # 3. 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"✅ 千牛连接功能: {'正常' if qianniu_success else '异常'}")
    print(f"✅ WebSocket服务器: {'运行中' if websocket_running else '未运行'}")
    print(f"✅ Flask服务器: {'运行中' if flask_running else '未运行'}")
    
    if qianniu_success and websocket_running:
        print("\n🎉 连接千牛功能完全正常！")
        print("\n📋 使用说明:")
        print("1. 千牛接待台已准备就绪")
        print("2. WebSocket服务器正在运行")
        print("3. 可以正常点击'连接千牛'按钮")
    elif qianniu_success and not websocket_running:
        print("\n⚠️ 千牛连接功能正常，但WebSocket服务器未运行")
        print("\n🔧 解决方案:")
        print("1. 确保主程序 (app.py) 正在运行")
        print("2. 检查程序启动日志是否有错误")
        print("3. 重新启动程序")
    else:
        print("\n❌ 连接千牛功能存在问题")
        print("\n🔧 故障排除:")
        print("1. 确保千牛接待台已启动并登录")
        print("2. 检查千牛窗口是否完全加载")
        print("3. 确认程序有足够的权限操作窗口")

if __name__ == "__main__":
    main()
