#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeepSeek接口的脚本
"""

import requests
import json

def test_deepseek_api(api_key, test_message="你好，请介绍一下你自己"):
    """
    测试DeepSeek API连接
    """
    if not api_key:
        print("❌ 请提供DeepSeek API密钥")
        return False
    
    url = "https://api.deepseek.com/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    payload = {
        "model": "deepseek-chat",
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的电商客服助手，请用友好和专业的语气回答客户问题。"
            },
            {
                "role": "user", 
                "content": test_message
            }
        ],
        "max_tokens": 500,
        "temperature": 0.8,
        "top_p": 0.5,
        "stream": False
    }
    
    try:
        print("🔄 正在测试DeepSeek API连接...")
        print(f"📝 测试消息: {test_message}")
        
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result['choices'][0]['message']['content']
            
            print("✅ DeepSeek API连接成功!")
            print(f"🤖 AI回复: {ai_response}")
            print(f"📈 使用tokens: {result.get('usage', {})}")
            
            return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"📄 错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，请检查网络连接")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return False
    except KeyError as e:
        print(f"❌ API响应格式错误: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False

def test_with_customer_service_scenario():
    """
    测试客服场景
    """
    api_key = input("请输入您的DeepSeek API密钥: ").strip()
    
    if not api_key:
        print("❌ 未提供API密钥，测试结束")
        return
    
    print("\n" + "="*50)
    print("🧪 DeepSeek API 客服场景测试")
    print("="*50)
    
    # 测试场景
    test_scenarios = [
        "你好，我想了解一下这个产品的规格",
        "这个商品什么时候发货？",
        "我想退货，请问怎么操作？",
        "有没有优惠活动？",
        "产品质量怎么样？"
    ]
    
    success_count = 0
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 测试场景 {i}/5:")
        if test_deepseek_api(api_key, scenario):
            success_count += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {success_count}/{len(test_scenarios)} 成功")
    
    if success_count == len(test_scenarios):
        print("🎉 所有测试通过！DeepSeek API集成成功")
    elif success_count > 0:
        print("⚠️ 部分测试通过，请检查网络或API配置")
    else:
        print("❌ 所有测试失败，请检查API密钥和网络连接")

if __name__ == "__main__":
    print("DeepSeek API 测试工具")
    print("=" * 30)
    
    choice = input("选择测试模式:\n1. 简单测试\n2. 客服场景测试\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        api_key = input("请输入您的DeepSeek API密钥: ").strip()
        test_deepseek_api(api_key)
    elif choice == "2":
        test_with_customer_service_scenario()
    else:
        print("❌ 无效选择")
