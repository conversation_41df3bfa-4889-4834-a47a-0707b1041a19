<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>千牛注入脚本测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background-color: #0056b3; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #0066cc; }
        .log-success { color: #009900; }
        .log-error { color: #cc0000; }
        .log-warning { color: #ff6600; }
    </style>
</head>
<body>
    <div class="container">
        <h1>千牛注入脚本测试页面</h1>
        
        <div class="status info">
            <strong>说明：</strong>此页面用于测试千牛消息拦截脚本是否正常工作
        </div>
        
        <div id="connection-status" class="status error">
            WebSocket连接状态：未连接
        </div>
        
        <div>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="sendTestMessage()">发送测试消息</button>
            <button onclick="clearLog()">清空日志</button>
            <button onclick="loadInjectionScript()">加载注入脚本</button>
        </div>
        
        <h3>实时日志</h3>
        <div id="log"></div>
        
        <h3>功能测试</h3>
        <div>
            <label>测试消息内容：</label>
            <input type="text" id="testMessage" value="你好，这是一条测试消息" style="width: 300px; padding: 5px;">
            <button onclick="sendCustomMessage()">发送自定义消息</button>
        </div>
    </div>

    <script>
        let ws = null;
        let logContainer = document.getElementById('log');
        let connectionStatus = document.getElementById('connection-status');

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            if (connected) {
                connectionStatus.className = 'status success';
                connectionStatus.textContent = 'WebSocket连接状态：已连接';
            } else {
                connectionStatus.className = 'status error';
                connectionStatus.textContent = 'WebSocket连接状态：未连接';
            }
        }

        function testConnection() {
            addLog('开始测试WebSocket连接...', 'info');
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                addLog('WebSocket已连接，关闭现有连接', 'warning');
                ws.close();
            }

            try {
                ws = new WebSocket('ws://localhost:8765');
                
                ws.onopen = function(event) {
                    addLog('WebSocket连接成功建立！', 'success');
                    updateConnectionStatus(true);
                };
                
                ws.onmessage = function(event) {
                    addLog(`收到服务器消息: ${event.data}`, 'success');
                };
                
                ws.onclose = function(event) {
                    addLog(`WebSocket连接已关闭 (代码: ${event.code})`, 'warning');
                    updateConnectionStatus(false);
                };
                
                ws.onerror = function(error) {
                    addLog(`WebSocket连接错误: ${error}`, 'error');
                    updateConnectionStatus(false);
                };
                
            } catch (error) {
                addLog(`创建WebSocket连接失败: ${error}`, 'error');
                updateConnectionStatus(false);
            }
        }

        function sendTestMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addLog('WebSocket未连接，请先测试连接', 'error');
                return;
            }

            const testData = {
                type: 'test',
                message: '这是一条测试消息',
                timestamp: new Date().toISOString()
            };

            try {
                ws.send(JSON.stringify(testData));
                addLog(`发送测试消息: ${JSON.stringify(testData)}`, 'info');
            } catch (error) {
                addLog(`发送消息失败: ${error}`, 'error');
            }
        }

        function sendCustomMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addLog('WebSocket未连接，请先测试连接', 'error');
                return;
            }

            const message = document.getElementById('testMessage').value;
            const customData = {
                type: 'custom',
                message: message,
                timestamp: new Date().toISOString()
            };

            try {
                ws.send(JSON.stringify(customData));
                addLog(`发送自定义消息: ${message}`, 'info');
            } catch (error) {
                addLog(`发送消息失败: ${error}`, 'error');
            }
        }

        function clearLog() {
            logContainer.innerHTML = '';
            addLog('日志已清空', 'info');
        }

        function loadInjectionScript() {
            addLog('尝试加载千牛注入脚本...', 'info');
            
            const script = document.createElement('script');
            script.src = 'https://localhost/imsupport';
            script.onload = function() {
                addLog('注入脚本加载成功！', 'success');
            };
            script.onerror = function() {
                addLog('注入脚本加载失败！可能是HTTPS证书问题或服务器未运行', 'error');
            };
            
            document.head.appendChild(script);
        }

        // 页面加载完成后自动测试
        window.onload = function() {
            addLog('测试页面已加载', 'info');
            addLog('点击"测试连接"按钮开始测试WebSocket连接', 'info');
            addLog('点击"加载注入脚本"按钮测试脚本加载', 'info');
        };
    </script>
</body>
</html>
