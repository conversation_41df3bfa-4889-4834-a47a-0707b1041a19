#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的AI回复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.database_manager import DatabaseManager
from src.Message import Message

def test_ai_replies():
    """测试新的AI回复效果"""
    print("🧪 测试新的AI回复效果")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        message = Message(db)
        
        # 测试消息案例
        test_cases = [
            "想做个网站计算费率的汇率",
            "你们的价格怎么样？",
            "什么时候能完成？",
            "你好，我想咨询一下服务",
            "能便宜点吗？",
            "有什么优惠活动吗？",
            "这个项目需要多长时间？",
            "你们技术怎么样？"
        ]
        
        print("📋 测试结果对比:")
        print("-" * 50)
        
        for i, test_msg in enumerate(test_cases, 1):
            print(f"\n{i}. 客户询问: {test_msg}")
            
            # 模拟消息数据
            test_data = {
                "ccode": f"test_session_{i}",
                "message": test_msg,
                "knowledge": None
            }
            
            try:
                system_info = db.get_system_info()
                
                # 检查智能回复是否启用
                ai_reply_enabled = system_info[14] if len(system_info) > 14 else True
                
                if ai_reply_enabled:
                    ai_reply = message.call_ai_service(test_data, system_info[10], system_info)
                    
                    if ai_reply:
                        print(f"   AI回复: {ai_reply}")
                        print(f"   字数: {len(ai_reply)} 字")
                        
                        # 检查回复质量
                        if len(ai_reply) <= 100:
                            print("   ✅ 回复长度合适")
                        else:
                            print("   ⚠️ 回复可能过长")
                            
                        if "亲" in ai_reply:
                            print("   ✅ 语气友好")
                        else:
                            print("   ℹ️ 可以更友好一些")
                    else:
                        print("   AI回复: (无回复)")
                else:
                    print("   ℹ️ 智能回复已关闭，将使用关键词匹配")
                    
            except Exception as e:
                print(f"   ❌ 测试失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def compare_old_vs_new():
    """对比新旧回复效果"""
    print("\n📊 新旧回复效果对比")
    print("=" * 50)
    
    print("❌ 旧版回复问题:")
    print("- 回复过于详细和技术性")
    print("- 提供具体的技术方案和步骤")
    print("- 回复内容过长，不适合客服场景")
    print("- 像技术文档而不是客服对话")
    
    print("\n✅ 新版回复优势:")
    print("- 简洁友好，控制在50字以内")
    print("- 主要用于安抚客户")
    print("- 引导客户等待人工客服")
    print("- 避免承诺具体细节")
    print("- 语气更加亲切自然")

def main():
    print("AI回复效果测试工具")
    print("=" * 50)
    
    print("🎯 测试目标:")
    print("- 验证AI回复是否简洁友好")
    print("- 检查回复长度是否合适")
    print("- 确认语气是否亲切")
    print("- 验证是否避免技术细节")
    
    # 测试AI回复
    if test_ai_replies():
        print("\n🎉 测试完成！")
        
        # 显示对比
        compare_old_vs_new()
        
        print("\n📋 使用建议:")
        print("1. 如果回复仍然过长，可以进一步调整提示词")
        print("2. 可以根据实际需要添加更多回复模板")
        print("3. 建议定期检查AI回复质量")
        print("4. 重要客户建议及时转人工客服")
        
    else:
        print("\n❌ 测试失败，请检查程序配置")

if __name__ == "__main__":
    main()
