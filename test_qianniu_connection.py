#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试千牛连接功能的脚本
"""

import win32gui
import win32con
import win32api
import time
import os
import pyautogui

def find_all_windows():
    """查找所有窗口"""
    windows = []
    
    def enum_windows_callback(hwnd, windows):
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            if window_text:  # 只显示有标题的窗口
                windows.append({
                    'hwnd': hwnd,
                    'title': window_text,
                    'class': class_name
                })
        return True
    
    win32gui.EnumWindows(enum_windows_callback, windows)
    return windows

def find_qianniu_windows():
    """查找千牛相关窗口"""
    print("🔍 查找千牛相关窗口...")
    windows = find_all_windows()
    
    qianniu_windows = []
    for window in windows:
        if ("千牛" in window['title'] or 
            "taobao" in window['title'].lower() or 
            "aliim" in window['title'].lower() or
            window['class'] == "Qt5152QWindowIcon"):
            qianniu_windows.append(window)
    
    if qianniu_windows:
        print(f"✅ 找到 {len(qianniu_windows)} 个千牛相关窗口:")
        for i, window in enumerate(qianniu_windows):
            print(f"  {i+1}. 标题: {window['title']}")
            print(f"     类名: {window['class']}")
            print(f"     句柄: {window['hwnd']}")
    else:
        print("❌ 未找到千牛相关窗口")
    
    return qianniu_windows

def find_child_window(parent_handle, child_window_title, index=1):
    """查找子窗口"""
    def callback(hwnd, hwnds):
        if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
            hwnds.append(hwnd)
        return True

    hwnds = []
    win32gui.EnumChildWindows(parent_handle, callback, hwnds)

    matched_hwnds = []
    for hwnd in hwnds:
        window_text = win32gui.GetWindowText(hwnd)
        if window_text == child_window_title:
            matched_hwnds.append(hwnd)
        # 递归查找子窗口
        child_hwnd = find_child_window(hwnd, child_window_title, index)
        if child_hwnd:
            matched_hwnds.append(child_hwnd)

    if len(matched_hwnds) >= index:
        return matched_hwnds[len(matched_hwnds)-1]
    return None

def test_qianniu_startup():
    """测试千牛启动"""
    print("\n🚀 测试千牛启动...")
    
    # 检查千牛是否已经运行
    parent_hwnd = win32gui.FindWindow("Qt5152QWindowIcon", "千牛接待台")
    
    if parent_hwnd:
        print("✅ 千牛接待台已经在运行")
        return parent_hwnd
    
    print("📱 千牛未运行，尝试启动...")
    try:
        os.startfile("aliim:login")
        print("✅ 已发送千牛启动命令")
        
        # 等待千牛启动
        for i in range(15):  # 等待最多15秒
            time.sleep(1)
            parent_hwnd = win32gui.FindWindow("Qt5152QWindowIcon", "千牛接待台")
            if parent_hwnd:
                print(f"✅ 千牛启动成功，窗口句柄: {parent_hwnd}")
                return parent_hwnd
            print(f"⏳ 等待千牛启动... ({i+1}/15)")
        
        print("❌ 千牛启动超时")
        return None
        
    except Exception as e:
        print(f"❌ 启动千牛失败: {str(e)}")
        return None

def test_qianniu_window_operations(parent_hwnd):
    """测试千牛窗口操作"""
    print(f"\n🖥️ 测试千牛窗口操作 (句柄: {parent_hwnd})...")
    
    try:
        # 显示窗口
        print("📺 显示千牛窗口...")
        win32gui.ShowWindow(parent_hwnd, win32con.SW_SHOWMAXIMIZED)
        time.sleep(1)
        
        # 查找子窗口
        print("🔍 查找千牛工作台子窗口...")
        child_hwnd = find_child_window(parent_hwnd, "千牛工作台", 2)
        
        if child_hwnd:
            print(f"✅ 找到千牛工作台子窗口，句柄: {child_hwnd}")
            
            # 获取窗口位置
            rect = win32gui.GetWindowRect(child_hwnd)
            x, y = rect[0], rect[1]
            click_x, click_y = x + 10, y + 10
            
            print(f"📍 窗口位置: ({x}, {y})")
            print(f"🖱️ 点击位置: ({click_x}, {click_y})")
            
            # 模拟点击
            print("🖱️ 模拟点击...")
            pyautogui.click(click_x, click_y)
            time.sleep(0.5)
            
            # 发送F5
            print("⌨️ 发送F5刷新...")
            pyautogui.hotkey('f5')
            
            print("✅ 千牛窗口操作完成")
            return True
        else:
            print("❌ 未找到千牛工作台子窗口")
            
            # 列出所有子窗口
            print("📋 列出所有子窗口:")
            def list_child_callback(hwnd, hwnds):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    if window_text or class_name:
                        print(f"  - 标题: '{window_text}', 类名: '{class_name}', 句柄: {hwnd}")
                return True
            
            win32gui.EnumChildWindows(parent_hwnd, list_child_callback, [])
            return False
            
    except Exception as e:
        print(f"❌ 窗口操作失败: {str(e)}")
        return False

def test_websocket_connection():
    """测试WebSocket连接"""
    print("\n🌐 测试WebSocket连接...")
    
    try:
        import websocket
        
        def on_open(ws):
            print("✅ WebSocket连接成功")
            ws.close()
        
        def on_error(ws, error):
            print(f"❌ WebSocket连接失败: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print("🔌 WebSocket连接已关闭")
        
        ws = websocket.WebSocketApp("ws://localhost:8765",
                                  on_open=on_open,
                                  on_error=on_error,
                                  on_close=on_close)
        
        # 设置超时
        ws.run_forever(ping_timeout=5)
        
    except ImportError:
        print("⚠️ websocket-client 模块未安装，跳过WebSocket测试")
    except Exception as e:
        print(f"❌ WebSocket测试失败: {str(e)}")

def main():
    print("千牛连接功能测试工具")
    print("=" * 50)
    
    # 1. 查找千牛窗口
    qianniu_windows = find_qianniu_windows()
    
    # 2. 测试千牛启动
    parent_hwnd = test_qianniu_startup()
    
    if not parent_hwnd:
        print("\n❌ 千牛启动失败，无法继续测试")
        print("\n💡 解决建议:")
        print("1. 手动启动千牛接待台")
        print("2. 确保千牛已正确安装")
        print("3. 检查系统是否支持 aliim:login 协议")
        return
    
    # 3. 测试窗口操作
    success = test_qianniu_window_operations(parent_hwnd)
    
    # 4. 测试WebSocket连接
    test_websocket_connection()
    
    # 5. 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"✅ 千牛窗口检测: {'成功' if qianniu_windows else '失败'}")
    print(f"✅ 千牛启动: {'成功' if parent_hwnd else '失败'}")
    print(f"✅ 窗口操作: {'成功' if success else '失败'}")
    
    if success:
        print("\n🎉 千牛连接功能测试通过！")
        print("\n📋 使用说明:")
        print("1. 确保千牛接待台已启动并登录")
        print("2. 在程序中点击'连接千牛'按钮")
        print("3. 程序会自动刷新千牛页面并建立连接")
    else:
        print("\n⚠️ 千牛连接功能存在问题")
        print("\n🔧 故障排除:")
        print("1. 检查千牛是否完全加载")
        print("2. 确认千牛工作台窗口存在")
        print("3. 检查窗口标题是否匹配")

if __name__ == "__main__":
    main()
