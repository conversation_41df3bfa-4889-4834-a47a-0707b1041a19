#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新AI提示词设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.database_manager import DatabaseManager

def update_prompts():
    """更新AI提示词"""
    print("🔧 更新AI提示词设置")
    print("=" * 50)
    
    try:
        db = DatabaseManager()
        
        # 新的简洁客服提示词
        new_general_prompt = """你是一个专业的在线客服，请遵循以下原则：

1. 保持友好和礼貌的语气
2. 回复要简洁明了，不要过于详细
3. 主要作用是安抚客户，等待人工客服上线
4. 不要提供具体的技术方案或详细步骤
5. 避免承诺具体的价格、时间或技术细节
6. 如果客户询问复杂问题，引导客户等待人工客服

常用回复模板：
- 对于询价：亲，我来为您详细介绍一下，请稍等片刻
- 对于技术问题：亲，这个问题比较专业，我安排专业客服为您详细解答
- 对于服务咨询：亲，我们会为您提供最优质的服务，请稍等
- 对于一般问题：亲，好的，我来为您查询一下

请用简短、友好的语气回复，每次回复控制在50字以内。"""

        # 更新通用AI提示词
        db.update_system_info(fastgpt_key=new_general_prompt)
        print("✅ 通用AI提示词已更新")
        
        # 显示更新后的提示词
        updated_info = db.get_system_info()
        print(f"\n📝 新的通用AI提示词:")
        print("-" * 30)
        print(updated_info[10])
        
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {str(e)}")
        return False

def test_new_prompt():
    """测试新的提示词效果"""
    print("\n🧪 测试新提示词效果")
    print("=" * 50)
    
    try:
        from src.Message import Message
        
        db = DatabaseManager()
        message = Message(db)
        
        # 测试消息
        test_cases = [
            "想做个网站计算费率的汇率",
            "你们的价格怎么样？",
            "什么时候能完成？",
            "你好，我想咨询一下服务"
        ]
        
        print("测试消息和AI回复:")
        print("-" * 30)
        
        for test_msg in test_cases:
            print(f"\n客户: {test_msg}")
            
            # 模拟消息数据
            test_data = {
                "ccode": "test_session",
                "message": test_msg,
                "knowledge": None
            }
            
            try:
                system_info = db.get_system_info()
                ai_reply = message.call_ai_service(test_data, system_info[10], system_info)
                
                if ai_reply:
                    print(f"AI回复: {ai_reply}")
                else:
                    print("AI回复: (智能回复已关闭)")
                    
            except Exception as e:
                print(f"测试失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    print("AI提示词优化工具")
    print("=" * 50)
    
    print("当前问题: AI回复过于详细和技术性")
    print("解决方案: 设置简洁的客服提示词")
    print()
    
    # 更新提示词
    if update_prompts():
        print("\n🎉 提示词更新成功！")
        
        # 询问是否测试
        choice = input("\n是否测试新的提示词效果？(y/n): ").strip().lower()
        if choice == 'y':
            test_new_prompt()
        
        print("\n📋 使用说明:")
        print("1. 重新启动程序以应用新的提示词")
        print("2. 新的AI回复将更加简洁友好")
        print("3. 主要用于安抚客户，等待人工客服")
        print("4. 避免提供详细的技术方案")
        
    else:
        print("\n❌ 提示词更新失败")

if __name__ == "__main__":
    main()
